<?php

namespace Modules\TelegramIstar\Helpers;

use <PERSON><PERSON><PERSON>\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramIstar\Models\WidthDrawl;

class IstarHelper
{
    public function getVipInfo(TelegramUser $user): array
    {
        $vipLevel = 0;

        if ($user->balance_star >= 1000000) {
            $vipLevel = 4;
        } elseif ($user->balance_star >= 500000) {
            $vipLevel = 3;
        } elseif ($user->balance_star >= 100000) {
            $vipLevel = 2;
        } elseif ($user->balance_star >= 10000) {
            $vipLevel = 1;
        }

        $vipEmojis = [
            0 => '👤',
            1 => '⭐',
            2 => '🌟',
            3 => '💎',
            4 => '👑',
        ];

        return [
            'vip_level' => $vipLevel,
            'vip_points' => $user->balance_star ?? 0,
            'vip_emoji' => $vipEmojis[$vipLevel] ?? '👤',
        ];
    }

    public function buildStarPurchaseMetaData(int $amount, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => 'star_purchase',
            'amount' => $amount,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    public function buildPremiumPurchaseMetaData(int $days, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => 'telegram_premium_purchase',
            'days' => $days,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    public function processPurchase(TelegramUser $user, array $payload): void
    {
        $type = $payload['type'] ?? null;
        switch ($type) {
            case 'star_purchase':
                $amount = $payload['amount'] ?? 0;
                $targetUsername = $payload['target_username'] ?? null;

                if ($amount > 0) {
                    if ($targetUsername) {
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $amount,
                            'currency' => 'STAR',
                            'address' => '@' . $targetUsername,
                        ]);
                    } else {
                        $user->addBalance($amount, 'STAR', 'purchase', 'Purchased ' . $amount . ' STAR', null);
                    }
                }
                break;
            case 'telegram_premium_purchase':
                $days = $payload['days'] ?? 0;
                $targetUsername = $payload['target_username'] ?? null;

                if ($days > 0) {
                    if ($targetUsername) {
                        // Create withdrawal request for gifting Telegram Premium to others
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'currency' => 'TELEGRAM_PREMIUM',
                            'address' => '@' . $targetUsername,
                            'type' => 'telegram_premium_gift',
                        ]);
                    } else {
                        // Create withdrawal request for user's own Telegram Premium
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'currency' => 'TELEGRAM_PREMIUM',
                            'address' => '@' . ($user->username ?: "user{$user->tele_id}"),
                            'type' => 'telegram_premium_purchase',
                        ]);
                    }
                }
                break;
            default:
                // Unknown type, do nothing
                break;
        }
    }
}
