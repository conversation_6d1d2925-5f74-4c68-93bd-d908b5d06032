<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Services\PremiumService;
use Modules\TelegramIstar\Components\BackToMenu;
use App\Services\SettingsService;
use Modules\TelegramTonPayment\Models\VerificationTransaction;
use Modules\TelegramIstar\Helpers\IstarHelper;

class BuyPremiumCommand implements CommandInterface
{
    protected $premiumService;

    public function __construct()
    {
        $this->premiumService = new PremiumService;
    }

    /**
     * Handle the buy Telegram Premium command (callback from premium shop menu)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;

            $premiumDays = $params[0] ?? null;
            $targetUsername = $params[1] ?? null; // For gifting to others

            if (!$premiumDays || !$this->premiumService->isValidPackage((int)$premiumDays)) {
                DiscordLogHelper::error('Invalid premium package: ' . $premiumDays);
                $this->sendInvalidPackageMessage($botClient, $chatId);
                return ['success' => true, 'handled' => true];
            }

            DiscordLogHelper::log('Processing buy Telegram Premium request for user ' . $user->tele_id . ' - Days: ' . $premiumDays . ($targetUsername ? ' - Target: ' . $targetUsername : ''));

            $istarHelper = new IstarHelper();
            $metadata = $istarHelper->buildPremiumPurchaseMetaData((int)$premiumDays, $targetUsername);
            $settingsService = new SettingsService();
            $address = $settingsService->getSetting('ton_wallet_address');

            $tonPrice = $this->premiumService->calculateTonPrice((int)$premiumDays);

            $transaction = $user->createVerificationTransaction($address, $tonPrice * 1_000_000_000, $metadata);

            $message = $this->createPaymentConfirmationMessage((int)$premiumDays, $user, $targetUsername);
            $keyboard = $this->createPaymentConfirmationKeyboard($transaction);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyPremiumCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $botClient = new TelegramBotClient;
            $botClient->sendMessage(
                $chatId,
                "❌ An error occurred while processing your Telegram Premium purchase request. Please try again later.",
                []
            );

            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Send invalid package message
     */
    private function sendInvalidPackageMessage(TelegramBotClient $botClient, string|int $chatId): void
    {
        $botClient->sendMessage(
            $chatId,
            "❌ Invalid Telegram Premium package selected. Please choose a valid package.",
            [
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [BackToMenu::make()],
                    ],
                ]),
            ]
        );
    }

    /**
     * Create payment confirmation message
     */
    private function createPaymentConfirmationMessage(int $premiumDays, TelegramUser $user, ?string $targetUsername = null): string
    {
        $usdPrice = $this->premiumService->calculateUsdPrice($premiumDays);
        $tonHelper = new TonHelper();
        $tonRate = $tonHelper->fetchTonPrice();
        $tonPrice = $usdPrice / $tonRate;

        $settingsService = new SettingsService();
        $tonReceiver = $settingsService->getSetting('ton_receiver_address');

        // Generate payment message format: username + usd_price + target (if gifting)
        $username = $user->username ?: "user{$user->tele_id}";
        $paymentMessage = "{$username}{$usdPrice}";
        if ($targetUsername) {
            $paymentMessage .= "_gift_{$targetUsername}";
        }

        $formattedTonPrice = number_format($tonPrice, 4);
        $formattedTonRate = number_format($tonRate, 2);
        $periodText = $this->premiumService->getPeriodText($premiumDays);

        // Package header based on whether it's a gift or personal purchase
        $packageHeader = $targetUsername
            ? "🎁 **Telegram Premium Gift Package**\n👤 Recipient: @{$targetUsername}\n\n"
            : "👑 **Telegram Premium Subscription Package**\n\n";

        $messages = [
            'en' => $packageHeader
                . "⏰ Duration: *{$periodText}*\n"
                . "💵 Price: *{$formattedTonPrice} TON* (\${$usdPrice})\n"
                . "💎 TON Rate: \${$formattedTonRate}\n\n"
                . "💰 *Payment Options:*\n\n"
                . "🚀 **Quick Pay**: Use the buttons below to open your TON wallet with pre-filled amount and message\n\n"
                . "📋 **Manual Pay**: Send exactly *{$formattedTonPrice} TON* to:\n"
                . "`{$tonReceiver}`\n"
                . "💬 Message: `{$paymentMessage}`\n\n"
                . "⚠️ *Important:* Send the exact amount and message to avoid delays\n\n",
        ];

        return $messages['en'];
    }

    /**
     * Create payment confirmation keyboard
     */
    private function createPaymentConfirmationKeyboard(VerificationTransaction $transaction): array
    {
        $tonLink = "ton://transfer/{$transaction->address}?amount={$transaction->nano_amount}&text=" . urlencode($transaction->verification_code);
        $tonkeeperLink = "https://app.tonkeeper.com/transfer/{$transaction->address}?amount={$transaction->nano_amount}&text=" . urlencode($transaction->verification_code);

        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => __('messages.buttons.pay_with_ton_wallet'),
                        'url' => $tonLink,
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.pay_with_tonkeeper'),
                        'url' => $tonkeeperLink,
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.payment_sent'),
                        'callback_data' => 'show_thank_you',
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.cancel'),
                        'callback_data' => 'cancel_buy-' . $transaction->id,
                    ],
                ],
            ],
        ];
    }
}
